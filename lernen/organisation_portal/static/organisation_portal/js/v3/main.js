var sidebarFull = 200;
var sidebarCollapsed = 80;
var LOGO_COLOR = "#43a2ad";
var OLD_STUDENTS_FEMALE_COLOR = "#bfe9ef";
var PAST_DAYS = 365 * 5;

$(document).ready(function() {
    registerSidebarMenu();
    // loadHomePage();
    $("#sidebar").addClass("collapsed");
    registerChangePassword();
    $("#bell-notification-li").css("display", "none");
});

function registerSidebarMenu() {
    registerHomeMenu();
    registerChangePassword();
    menuLoader.registerSidebarMenu();
    // activateMenuItem();
}

function registerHomeMenu() {
    $('#homeNav').on('click', function() {
        loadHomePage();
    });
}

function registerChangePassword() {
    $('#changePasswordNav').on('click', function() {
        loadChangePasswordPage();
    });
}

function loadHomePage() {
    ajaxClient.get("/organisation-portal/home", function(data) {
        $("#main-content").html(data);

        $("input#stats-date").daterangepicker({
          autoApply : true,
          singleDatePicker: true,
          showDropdowns: true,
          minDate: moment().startOf("day").subtract(PAST_DAYS, "days"),
          maxDate: moment().startOf("day"),
          locale: {
            format: inputDatePickerFormat,
            cancelLabel: 'Clear'
          },
          onSelect: function(dateText) {
            $(this).change();
          }
        }).on("change", function() {
            homePage.refreshHomePage();
        });

        homePage.displayDashboardContent();
        academicSessionHandler.bindSessionChangeEvent(homePage.refreshHomePage);
    });
}

var homePageV2 = {

    loadHomePage : function() {
      ajaxClient.get("/organisation-portal/home/<USER>", function(data) {
            $("#main-content").html(data);

            $("input#stats-date-range").daterangepicker({
              autoApply: false,
              showDropdowns: true,
              startDate: moment().startOf("day"),
              endDate: moment().startOf("day"),
              minDate: moment().startOf("day").subtract(PAST_DAYS, "days"),
              maxDate: moment().startOf("day"),
              locale: {
                format: inputDatePickerFormat,
                cancelLabel: 'Clear'
              },
              ranges: {
                'Today': [moment(), moment()],
                'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                'This Month': [moment().startOf('month'), moment().endOf('month')],
                'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
              }
            }).on("change", function() {
              homePageV2.loadWidgets();
            });
        });
    },

    loadWidget : function(widgetClassDict, url, renderFunction) {
      for(var widgetClass in widgetClassDict) {
        console.log("displaying loader for : " + widgetClass);
        homePageV2.displayWidgetLoader(widgetClassDict[widgetClass]);
      }

      ajaxClient.get(url, function(data) {
          renderFunction(widgetClassDict, data);
          for(var widgetClass in widgetClassDict) {
              homePageV2.hideWidgetLoader(widgetClassDict[widgetClass]);
          }
      }, true);
     
    },

    loadWidgets : function(){
      var studentAttendanceWidgetClass = ".student-attendance-stats";
      var studentCountWidgetClass = ".student-count-stats";
      var staffCountWidgetClass = ".staff-count-stats";
      var staffAttendanceWidgetClass = ".staff-attendance-stats";
      var studentAdmissionWidgetClass = ".student-admission-stats";
      var totalFeeCollectionWidgetClass = ".fee-collection-stats";

      homePageV2.loadWidget({'studentAttendanceWidgetClass' : studentAttendanceWidgetClass, 'studentCountWidgetClass' : studentCountWidgetClass}, "/organisation-portal/stats/v2/student-attendance", homePageV2.renderStudentAttendanceAndMetadata);
      homePageV2.loadWidget({'staffCountWidgetClass' : staffCountWidgetClass, 'staffAttendanceWidgetClass' : staffAttendanceWidgetClass}, "/organisation-portal/stats/v2/staff-attendance", homePageV2.renderStaffAttendance);
      homePageV2.loadWidget({'studentAdmissionWidgetClass' : studentAdmissionWidgetClass}, "/organisation-portal/stats/v2/student-admission", homePageV2.renderStudentAdmissionStats);
      homePageV2.loadWidget({'totalFeeCollectionWidgetClass' : totalFeeCollectionWidgetClass}, "/organisation-portal/stats/v2/fee-collection", homePageV2.renderFeeCollectionStats);
    },

    displayWidgetLoader : function(widgetClass){
      $(widgetClass).find(".stats-widget-loader").attr("style", "display:block;");
      $(widgetClass).find(".stats-widget-content").attr("style", "display:none;");
    },

    hideWidgetLoader : function(widgetClass){
      $(widgetClass).find(".stats-widget-loader").attr("style", "display:none;");
      $(widgetClass).find(".stats-widget-content").attr("style", "display:block;");
    },


    renderStudentAttendanceAndMetadata : function(widgetClassDict, studentStats) {
      var studentAttendanceWidgetClass = widgetClassDict.studentAttendanceWidgetClass;
      var studentCountWidgetClass = widgetClassDict.studentCountWidgetClass;
      $(studentCountWidgetClass).find(".stats-widget-content").text(studentStats.totalStudent);
      var presentCount = 0;
      var presentTotalCount = 0;
      var leaveCount = 0;
      var leaveTotalCount = 0;
      studentAttendanceCounts = studentStats.studentAttendanceCounts[0];
      for (var i = 0; i < studentAttendanceCounts.attendanceCounts.length; i++) {
        if (studentAttendanceCounts.attendanceCounts[i].attendanceStatus == "PRESENT") {
          presentCount = studentAttendanceCounts.attendanceCounts[i].count;
          presentTotalCount = studentAttendanceCounts.attendanceCounts[i].totalCount;
        }
        if (studentAttendanceCounts.attendanceCounts[i].attendanceStatus == "LEAVE") {
          leaveCount = studentAttendanceCounts.attendanceCounts[i].count;
          leaveTotalCount = studentAttendanceCounts.attendanceCounts[i].totalCount;
        }
      }

      const $carousel = $(`
        <div id="carouselExampleControls" class="carousel slide" data-ride="carousel">
          <div class="carousel-inner"></div>
          <a class="carousel-control-prev" href="#carouselExampleControls" role="button" data-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true" style="background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'black\' viewBox=\'0 0 8 8\'%3E%3Cpath d=\'M5.25 0L3.82 1.41 6.41 4 3.82 6.59 5.25 8 9.25 4z\'/%3E%3C/svg%3E');"></span>
            <span class="sr-only">Previous</span>
          </a>
          <a class="carousel-control-next" href="#carouselExampleControls" role="button" data-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true" style="background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'black\' viewBox=\'0 0 8 8\'%3E%3Cpath d=\'M2.75 0L4.18 1.41 1.59 4 4.18 6.59 2.75 8 -1.25 4z\'/%3E%3C/svg%3E');"></span>
            <span class="sr-only">Next</span>
          </a>
        </div>
      `);

      const $carouselInner = $carousel.find('.carousel-inner');
      console.log("========================");
      studentStats.studentAttendanceCounts.forEach((attendanceTypeEntry, index) => {
        var attendanceType = attendanceTypeEntry.attendanceType;
        console.log(attendanceType);
        const present = attendanceTypeEntry.attendanceCounts.find(a => a.attendanceStatus === "PRESENT");
        const leave = attendanceTypeEntry.attendanceCounts.find(a => a.attendanceStatus === "LEAVE");
        console.log(present);
        console.log(leave);
        const percentPresent = ((present.count / present.total) * 100).toFixed(1);

        const $item = $(`
          <div class="carousel-item ${index === 0 ? 'active' : ''}">
            <div class="card-body py-4">
              <div class="media">
                <div class="media-body">
                  <h4 class="mb-2 stats-widget-content">${present.count} / ${present.total} (${percentPresent}%)</h4>
                  <p class="mb-2">Student Attendance</p>
                  <div class="mb-0 mt-3" >
                    <span class="badge badge-soft-success" >
                      <i class="mdi mdi-arrow-bottom-right"></i>
                      ${attendanceType}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        `);
        console.log($item);
        $carouselInner.append($item);
        
        // create label and data array from payload
        var labelArr = [];
        var dataArr = [];
        var totalStudentCount = 0;

        // Extract data from institute level about total count per institute

        studentStats.studentCount.forEach((instituteEntry) => {
          var instituteName = instituteEntry.instituteId;
          var totalCount = instituteEntry.value;
          labelArr.push(instituteName);
          dataArr.push(totalCount);
          totalStudentCount += totalCount;
        });

        homePageV2.renderStudentCountPieChart(labelArr, dataArr, totalStudentCount);
      });

      console.log($carouselInner);
      $(studentAttendanceWidgetClass).find('.stats-widget-content').html($carousel);

      if (window.feather) {
        feather.replace(); // render feather icons
      }
    },

    renderStaffAttendance : function(widgetClassDict, staffStats) {
      var staffAttendanceWidgetClass = widgetClassDict.staffAttendanceWidgetClass;
      var staffCountWidgetClass = widgetClassDict.staffCountWidgetClass;

      // Extract overall present status from staffAttendanceCounts
      var presentCount = 0;
      var leaveCount = 0;
      var totalCount = 0;

      if (staffStats.staffAttendanceCounts && staffStats.staffAttendanceCounts.length > 0) {
        var attendanceCounts = staffStats.staffAttendanceCounts[0].attendanceCounts;

        // Find present status
        var presentStatus = attendanceCounts.find(item => item.attendanceStatus === "PRESENT");
        if (presentStatus) {
          presentCount = presentStatus.count;
          totalCount = presentStatus.total;
        }
        // Find leave status
        var leaveStatus = attendanceCounts.find(item => item.attendanceStatus === "LEAVE");
        if (leaveStatus) {
          leaveCount = leaveStatus.count;
        }

      }

      // Create label array for institute and gender wise staff count
      var labelArr = [];
      var maleDataArr = [];
      var femaleDataArr = [];

      // Create a map of institute ID to gender counts
      var instituteGenderMap = {};

      // First, collect all unique institute IDs
      var instituteIds = [...new Set(staffStats.staffCountByGender.map(entry => entry.instituteId))];

      // Initialize map with 0 counts for all institutes
      instituteIds.forEach(instituteId => {
        instituteGenderMap[instituteId] = { male: 0, female: 0 };
      });

      // Populate the map with actual counts
      staffStats.staffCountByGender.forEach((entry) => {
        if (entry.gender === 'MALE') {
          instituteGenderMap[entry.instituteId].male = entry.count;
        } else if (entry.gender === 'FEMALE') {
          instituteGenderMap[entry.instituteId].female = entry.count;
        }
      });

      // Create arrays for chart data
      instituteIds.forEach(instituteId => {
        labelArr.push(instituteId);
        maleDataArr.push(instituteGenderMap[instituteId].male);
        femaleDataArr.push(instituteGenderMap[instituteId].female);
      });

      // Display present count / total count format
      var displayText = presentCount + " / " + totalCount;
      $(staffCountWidgetClass).find(".stats-widget-content").text(totalCount);
      $(staffAttendanceWidgetClass).find(".stats-widget-content-staff-present").text(presentCount);
      $(staffAttendanceWidgetClass).find(".stats-widget-content-staff-leave").text(leaveCount);

      homePageV2.renderStaffGenderDistributionChart(labelArr, maleDataArr, femaleDataArr);
    },

    renderStudentAdmissionStats : function(widgetClassDict, studentStats) {
      console.log(studentStats);
      console.log("-----------------------");
      var studentAdmissionWidgetClass = widgetClassDict.studentAdmissionWidgetClass;

      $(studentAdmissionWidgetClass).find(".stats-widget-content-admission").text(studentStats.totalNewAdmissions);
      $(studentAdmissionWidgetClass).find(".stats-widget-content-tc").text(studentStats.totalTCIssued);
    },

    renderFeeCollectionStats : function(widgetClassDict, feeStats) {
      var totalFeeCollectionWidgetClass = widgetClassDict.totalFeeCollectionWidgetClass;
      $(totalFeeCollectionWidgetClass).find(".stats-widget-content").text(feeStats.totalAmount +"/-");

      // Process paymentModeCollections array to create label and data arrays
      var labelArr = [];
      var dataArr = [];

      if (feeStats.paymentModeCollections && Array.isArray(feeStats.paymentModeCollections)) {
        feeStats.paymentModeCollections.forEach(function(collection) {
          labelArr.push(collection.mode);
          dataArr.push(collection.value);
        });
      }

      // Process instituteStats array to create stacked bar chart data
      var instituteLabelArr = [];
      var feeHeadDatasetArr = [];
      var allFeeHeads = [];
      var feeHeadColorMap = {};

      if (feeStats.instituteStats && Array.isArray(feeStats.instituteStats)) {
        // Define color palette for dynamic fee heads
        var colorPalette = [
          window.theme.success,
          window.theme.warning,
          window.theme.danger,
          window.theme.info,
          "#2e7078",
          "#43a2ad",
          "#7fadb2",
          "#a5d4d9",
          "#afdbe0",
          "#e7f9fb",
          "#108F2B",
          "#C08143",
          "#DCDF69",
          "#719E90",
          "#A8B9DF",
          "#B74034",
          "#F196CD",
          "#6486B9",
          "#5551FA",
          "#E8F697",
          "#0591F6",
          "#4C6C42",
          "#442BC6"
        ];

        // First pass: collect all unique fee heads and assign colors
        feeStats.instituteStats.forEach(function(institute) {
          instituteLabelArr.push(institute.instituteName);

          if (institute.feeHeadCollections && Array.isArray(institute.feeHeadCollections)) {
            institute.feeHeadCollections.forEach(function(feeHead) {
              if (allFeeHeads.indexOf(feeHead.feeHead) === -1) {
                allFeeHeads.push(feeHead.feeHead);
                feeHeadColorMap[feeHead.feeHead] = colorPalette[(allFeeHeads.length - 1) % colorPalette.length];
              }
            });
          }
        });

        // Second pass: create datasets for each fee head
        allFeeHeads.forEach(function(feeHead) {
          var dataForThisFeeHead = [];

          feeStats.instituteStats.forEach(function(institute) {
            var valueForThisFeeHead = 0;

            if (institute.feeHeadCollections && Array.isArray(institute.feeHeadCollections)) {
              var foundFeeHead = institute.feeHeadCollections.find(function(fh) {
                return fh.feeHead === feeHead;
              });
              if (foundFeeHead) {
                valueForThisFeeHead = foundFeeHead.value;
              }
            }

            dataForThisFeeHead.push(valueForThisFeeHead);
          });

          feeHeadDatasetArr.push({
            label: feeHead,
            backgroundColor: feeHeadColorMap[feeHead],
            borderColor: feeHeadColorMap[feeHead],
            hoverBackgroundColor: feeHeadColorMap[feeHead],
            hoverBorderColor: feeHeadColorMap[feeHead],
            data: dataForThisFeeHead,
            barPercentage: .325,
            categoryPercentage: .5
          });
        });
      }

      homePageV2.renderFeePaymentModeCollectionPieChart(labelArr, dataArr, feeStats.totalAmount);
      homePageV2.renderFeeCollectionFeeHeadDistributionChart(instituteLabelArr, feeHeadDatasetArr);
    },

    renderStudentCountPieChart : function (labelArr, dataArr, totalStudentCount) {
     new Chart($("#chartjs-student-count-pie"), {
       type: "pie",
       data: {
         labels: labelArr,
         datasets: [{
           data: dataArr,
           backgroundColor: ["#2e7078", "#43a2ad", "#7fadb2", "#a5d4d9", "#afdbe0", "#e7f9fb"],
           borderWidth: 1,
           borderColor: window.theme.white
         }]
       },
       options: {
         responsive: !window.MSInputMethodContext,
         maintainAspectRatio: false,
         cutoutPercentage: 70,
         legend: {
           display: false
         }
       },
       plugins: [{
        id: 'customPlugin',
        beforeDraw: (chart, args, options) => {
          var width = chart.chart.width,
              height = chart.chart.height,
              ctx = chart.chart.ctx;

          ctx.restore();
          var fontSize = (height / 114).toFixed(2);
          ctx.font = fontSize + "em sans-serif";
          ctx.textBaseline = "middle";

          var text = totalStudentCount,
              textX = Math.round((width - ctx.measureText(text).width) / 2),
              textY = height / 2;

          ctx.fillText(text, textX, textY);
          ctx.save();
        }
      }]
     });
   },
    
   renderStaffGenderDistributionChart : function (labelArr, maleDataArr, femaleDataArr ) {
     console.log(labelArr);
     console.log(maleDataArr);
     console.log(femaleDataArr);
     // Bar chart
     new Chart($("#chartjs-staff-gender-distribution"), {
       type: "bar",
       data: {
         labels: labelArr,
         datasets: [
              {
                label: "Male",
                backgroundColor: window.theme.success,
                borderColor: window.theme.success,
                hoverBackgroundColor: window.theme.success,
                hoverBorderColor: window.theme.success,
                data: maleDataArr,
                barPercentage: .325,
                categoryPercentage: .5
              },{
              label: "Female",
              backgroundColor: window.theme.warning,
              borderColor: window.theme.warning,
              hoverBackgroundColor: window.theme.warning,
              hoverBorderColor: window.theme.warning,
              data: femaleDataArr,
              barPercentage: .325,
              categoryPercentage: .5
            }
         ]
       },
       options: {
         maintainAspectRatio: false,
         cornerRadius: 15,
         legend: {
           display: true
         },
         scales: {
           yAxes: [{
             ticks: {
                  beginAtZero: true
             },
             gridLines: {
               display: false
             },
             stacked: false,
             stacked: true,
           }],
           xAxes: [{
             stacked: false,
             gridLines: {
               color: "transparent"
             },
             stacked: true,
           }]
         }
       }
     });
   },

  renderFeePaymentModeCollectionPieChart : function (labelArr, dataArr, totalAmount) {
     new Chart($("#chartjs-fee-collection-payment-mode-pie"), {
       type: "pie",
       data: {
         labels: labelArr,
         datasets: [{
           data: dataArr,
           backgroundColor: ["#2e7078", "#43a2ad", "#7fadb2", "#a5d4d9", "#afdbe0", "#e7f9fb"],
           borderWidth: 1,
           borderColor: window.theme.white
         }]
       },
       options: {
         responsive: !window.MSInputMethodContext,
         maintainAspectRatio: false,
         cutoutPercentage: 70,
         legend: {
           display: false
         }
       },
       plugins: [{
        id: 'customPlugin',
        beforeDraw: (chart, args, options) => {
          var width = chart.chart.width,
              height = chart.chart.height,
              ctx = chart.chart.ctx;

          ctx.restore();
          var fontSize = (height / 114).toFixed(2);
          ctx.font = fontSize + "em sans-serif";
          ctx.textBaseline = "middle";

          var text = totalAmount,
              textX = Math.round((width - ctx.measureText(text).width) / 2),
              textY = height / 2;

          ctx.fillText(text, textX, textY);
          ctx.save();
        }
      }]
     });
   },

   renderFeeCollectionFeeHeadDistributionChart : function (labelArr, feeHeadDatasetArr ) {
     console.log("Institute Labels:", labelArr);
     console.log("Fee Head Datasets:", feeHeadDatasetArr);

     // Stacked Bar chart
     new Chart($("#chartjs-fee-collection-fee-head-distribution"), {
       type: "bar",
       data: {
         labels: labelArr,
         datasets: feeHeadDatasetArr
       },
       options: {
         maintainAspectRatio: false,
         cornerRadius: 15,
         legend: {
           display: true,
           position: 'top',
           align: 'start',
           labels: {
             boxWidth: 12,
           }
         },
         scales: {
           yAxes: [{
             ticks: {
                  beginAtZero: true
             },
             gridLines: {
               display: false
             },
             stacked: true  // Enable stacking for Y-axis
           }],
           xAxes: [{
             stacked: true,  // Enable stacking for X-axis
             gridLines: {
               color: "transparent"
             }
           }]
         },
         tooltips: {
           mode: 'index',
           intersect: false,
           callbacks: {
             title: function(tooltipItems, data) {
               return 'Institute: ' + data.labels[tooltipItems[0].index];
             },
             label: function(tooltipItem, data) {
               var label = data.datasets[tooltipItem.datasetIndex].label || '';
               if (label) {
                 label += ': ';
               }
               label += tooltipItem.yLabel.toLocaleString();
               return label;
             },
             footer: function(tooltipItems, data) {
               var total = 0;
               tooltipItems.forEach(function(tooltipItem) {
                 total += tooltipItem.yLabel;
               });
               return 'Total: ' + total.toLocaleString();
             }
           }
         }
       }
     });
   },

};

var homePage =  {

    refreshHomePage : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var date = getDate($('input#stats-date').val()).getTime()/1000;
      ajaxClient.get("/organisation-portal/home-date-session-change/"+ date + "/" + academicSessionId, function(data) {
        $("#attendance-dashboard-session-content").html(data);
        homePage.displayDashboardContent();
      });
    },

   displayDashboardContent : function () {
     var instituteStats = readJson("#home-page-stats");
     var totalAssignedAmount = 0;
     var totalCollectedAmount = 0;

     var labelArr = [];
     var dataArr = [];
     var instituteNameWithUinqueCode = [];
     var feeHeadLable = [];
     var feeHeadMap = {};
     var total = 0;

     var totalStudentCount = 0;
     var attendanceDataArr = [];

     var totalActiveTransportStudentCount = 0;
     var transportDataArr = [];

     var maleStaff = [];
     var femaleStaff = [];

     var totalFeeDataArr = []
     var collectedFeeDataArr = []
     var discountFeeDataArr = []
     var dueFeeDataArr = []

     console.log("test");
     var todayStaffPresentArr = []
     var totalStaffPresent = 0;

     for (instituteId in instituteStats) {

       var instituteData = instituteStats[instituteId];

       institute = instituteData['institute'];
       var instituteDisplayName = institute['branchName'];
       var instituteUinqueCode = institute['instituteUniqueCode'];
       if(instituteDisplayName == null || instituteDisplayName == ""){
           instituteDisplayName = institute['instituteName']
       }
       labelArr.push(instituteDisplayName);
       //this is created as instituteDisplayName can be same for different branches. It is used in fee head count
       var instituteNameUinqueCode = instituteUinqueCode+":"+instituteDisplayName;
       instituteNameWithUinqueCode.push(instituteNameUinqueCode);
       dataArr.push(instituteData['total_students']);

       var attendance_stats = instituteData['attendance_stats'];
       var totalPresentAttendance = 0;
       if(attendance_stats != null) {
        totalPresentAttendance = parseFloat(attendance_stats.totalPresentAttendance.toFixed(2));
       }
       totalStudentCount += totalPresentAttendance;
       attendanceDataArr.push(parseFloat(totalPresentAttendance.toFixed(2)));

       var transport_stat = instituteData['transport_stats'];
       var activeTransportStudentCount = 0;
       if(transport_stat != null) {
        activeTransportStudentCount = transport_stat.totalTransportAssignedStudentCount;
       }
       transportDataArr.push(activeTransportStudentCount);
       totalActiveTransportStudentCount += activeTransportStudentCount;

        var staff_stats = instituteData['staff_stats'];
        if (staff_stats != null) {
          totalStaffPresent += staff_stats.todayTotalPresentStaff;
          todayStaffPresentArr.push(staff_stats.todayTotalPresentStaff);
          var staffGenderCountMap = staff_stats.staffGenderWiseCount;
          if (staffGenderCountMap != null) {
            var genderKeys = Object.keys(staffGenderCountMap);

            for (var i = 0; i < genderKeys.length; i++) {
              var gender = genderKeys[i];
              if (gender === 'MALE') {
                maleStaff.push(staffGenderCountMap[gender]);
              } else if (gender === 'FEMALE') {
                femaleStaff.push(staffGenderCountMap[gender]);
              }
            }
          }
        }

       var class_payment_stats = instituteData['class_payment_stats']
       if(class_payment_stats != null) {
         totalFeeDataArr.push(parseFloat(class_payment_stats.assignedAmount.toFixed(2)));
         collectedFeeDataArr.push(parseFloat(class_payment_stats.collectedAmount.toFixed(2)));
         discountFeeDataArr.push(parseFloat(class_payment_stats.discountAmount.toFixed(2)));
         dueFeeDataArr.push(parseFloat(class_payment_stats.dueAmount.toFixed(2)));
         var feeHeads = class_payment_stats.feeHeadCollectedAmountMap;
         for (const [feeHead, amount] of Object.entries(feeHeads)) {
            if (!feeHeadMap[feeHead]) {
                feeHeadLable.push(feeHead);
                feeHeadMap[feeHead] = {};
            }
            if (!feeHeadMap[feeHead][instituteNameUinqueCode]) {
              feeHeadMap[feeHead][instituteNameUinqueCode] = 0;
            }
            // Sum the amounts if multiple entries exist for the same institute and feeHead
            feeHeadMap[feeHead][instituteNameUinqueCode] += amount;
          }
       } else {
         totalFeeDataArr.push(0);
         collectedFeeDataArr.push(0);
         discountFeeDataArr.push(0);
         dueFeeDataArr.push(0);
       }
     }

     homePage.renderStudentCountChart(labelArr, dataArr);
     homePage.renderPresentAttendancePieChart(labelArr, attendanceDataArr, totalStudentCount);
     homePage.renderStudentFeesCountChart(labelArr, totalFeeDataArr, collectedFeeDataArr, discountFeeDataArr, dueFeeDataArr);
     homePage.renderStudentFeeHeadsCountChart(feeHeadLable, instituteNameWithUinqueCode, feeHeadMap);
     homePage.renderTransportAssignedPieChart(labelArr, transportDataArr, totalActiveTransportStudentCount);
     homePage.renderStaffGenderCountChart(labelArr, maleStaff, femaleStaff);
     homePage.renderStaffTodayAttendanceChart(labelArr, todayStaffPresentArr, totalStaffPresent);
   },

   renderStudentCountChart : function (labelArr, dataArr) {
     // Bar chart
     new Chart($("#chartjs-student-distribution"), {
       type: "bar",
       data: {
         labels: labelArr,
         datasets: [{
           label: "Student Count",
           backgroundColor: PRIMARY_LOGO_COLOR,
           borderColor: PRIMARY_LOGO_COLOR,
           hoverBackgroundColor: PRIMARY_LOGO_COLOR,
           hoverBorderColor: PRIMARY_LOGO_COLOR,
           data: dataArr,
           barPercentage: .325,
           categoryPercentage: .5
         }],
       },
       options: {
         maintainAspectRatio: false,
         cornerRadius: 15,
         legend: {
           display: false
         },
         scales: {
           yAxes: [{
             ticks: {
                  beginAtZero: true
             },
             gridLines: {
               display: false
             },
             stacked: false,
             stacked: true,
           }],
           xAxes: [{
             stacked: false,
             gridLines: {
               color: "transparent"
             },
             stacked: true,
           }]
         },
        animation: {
				onComplete: function () {
					var chartInstance = this.chart,
						ctx = chartInstance.ctx;
						ctx.textAlign = 'center';
						ctx.fillStyle = "rgba(0, 0, 0, 1)";
						ctx.textBaseline = 'bottom';

						this.data.datasets.forEach(function (dataset, i) {
							var meta = chartInstance.controller.getDatasetMeta(i);
							meta.data.forEach(function (bar, index) {
								var data = dataset.data[index];
								ctx.fillText(data, bar._model.x, bar._model.y - 5);

							});
						});
					}
				},
        events: [],
       }
     });
   },

   renderPresentAttendancePieChart : function (labelArr, dataArr, totalStudentCount) {
     new Chart($("#chartjs-present-attendance-pie"), {
       type: "pie",
       data: {
         labels: labelArr,
         datasets: [{
           data: dataArr,
           backgroundColor: ["#2e7078", "#43a2ad", "#7fadb2", "#a5d4d9", "#afdbe0", "#e7f9fb"],
           borderWidth: 1,
           borderColor: window.theme.white
         }]
       },
       options: {
         responsive: !window.MSInputMethodContext,
         maintainAspectRatio: false,
         cutoutPercentage: 70,
         legend: {
           display: false
         }
       },
       plugins: [{
        id: 'customPlugin',
        beforeDraw: (chart, args, options) => {
          var width = chart.chart.width,
              height = chart.chart.height,
              ctx = chart.chart.ctx;

          ctx.restore();
          var fontSize = (height / 114).toFixed(2);
          ctx.font = fontSize + "em sans-serif";
          ctx.textBaseline = "middle";

          var text = totalStudentCount,
              textX = Math.round((width - ctx.measureText(text).width) / 2),
              textY = height / 2;

          ctx.fillText(text, textX, textY);
          ctx.save();
        }
      }]
     });
   },

   renderTransportAssignedPieChart : function (labelArr, dataArr, totalStudentCount) {
    new Chart($("#chartjs-active-transport-pie"), {
      type: "pie",
      data: {
        labels: labelArr,
        datasets: [{
          data: dataArr,
          backgroundColor: ["#2e7078", "#43a2ad", "#7fadb2", "#a5d4d9", "#afdbe0", "#e7f9fb"],
          borderWidth: 1,
          borderColor: window.theme.white
        }]
      },
      options: {
        responsive: !window.MSInputMethodContext,
        maintainAspectRatio: false,
        cutoutPercentage: 70,
        legend: {
          display: false
        }
      },
      plugins: [{
       id: 'customPlugin',
       beforeDraw: (chart, args, options) => {
         var width = chart.chart.width,
             height = chart.chart.height,
             ctx = chart.chart.ctx;

         ctx.restore();
         var fontSize = (height / 114).toFixed(2);
         ctx.font = fontSize + "em sans-serif";
         ctx.textBaseline = "middle";

         var text = totalStudentCount,
             textX = Math.round((width - ctx.measureText(text).width) / 2),
             textY = height / 2;

         ctx.fillText(text, textX, textY);
         ctx.save();
       }
     }]
    });
  },

  renderStaffGenderCountChart : function (labelArr, maleStaff, femaleStaff) {
    Chart.Legend.prototype.afterFit = function() {
      this.height = this.height + 20;
    };
    // Bar chart
    var newSatffStatsChart = new Chart($("#chartjs-genderwise-staff-bar-distribution"), {
      type: "bar",
      data: {
        labels: labelArr,
        datasets: [
          {
           label: "Male Staff",
           backgroundColor: LOGO_COLOR,
           borderColor: LOGO_COLOR,
           hoverBackgroundColor: LOGO_COLOR,
           hoverBorderColor: LOGO_COLOR,
           data: maleStaff,
           barPercentage: .325,
           categoryPercentage: .5
         },
         {
          label: "Female Staff",
          backgroundColor: OLD_STUDENTS_FEMALE_COLOR,
          borderColor: OLD_STUDENTS_FEMALE_COLOR,
          hoverBackgroundColor: OLD_STUDENTS_FEMALE_COLOR,
          hoverBorderColor: OLD_STUDENTS_FEMALE_COLOR,
          data: femaleStaff,
          barPercentage: .325,
          categoryPercentage: .5
        }
   ]},
  options: {
    responsive: !window.MSInputMethodContext,
    maintainAspectRatio: false,
    cornerRadius: 15,
    legend: {
      display: true,
      position: 'top',
      align: 'start',
      labels:{
        boxWidth: 12,
      }
    },
    scales: {
      yAxes: [{
        ticks: {
             beginAtZero: true
        },
        gridLines: {
          display: false
        },
        stacked: false,
        stacked: true,
      }],
      xAxes: [{
        stacked: false,
        gridLines: {
          color: "transparent"
        },
        stacked: true,
      }]
    }
  }
  });
  newSatffStatsChart.canvas.parentNode.style.height = '311px';
  newSatffStatsChart.canvas.parentNode.style.width = '311px';
  },

   renderStudentFeesCountChart : function (labelArr, totalFeeDataArr, collectedFeeDataArr, discountFeeDataArr, dueFeeDataArr ) {
     // Bar chart
     new Chart($("#chartjs-institute-fee-distribution"), {
       type: "bar",
       data: {
         labels: labelArr,
         datasets: [{
            label: "Collected Fees",
            backgroundColor: window.theme.success,
            borderColor: window.theme.success,
            hoverBackgroundColor: window.theme.success,
            hoverBorderColor: window.theme.success,
            data: collectedFeeDataArr,
            barPercentage: .325,
            categoryPercentage: .5
          },{
           label: "Discounted Fees",
           backgroundColor: window.theme.warning,
           borderColor: window.theme.warning,
           hoverBackgroundColor: window.theme.warning,
           hoverBorderColor: window.theme.warning,
           data: discountFeeDataArr,
           barPercentage: .325,
           categoryPercentage: .5
         },
         {
          label: "Due Fees",
          backgroundColor: window.theme.danger,
          borderColor: window.theme.danger,
          hoverBackgroundColor: window.theme.danger,
          hoverBorderColor: window.theme.danger,
          data: dueFeeDataArr,
          barPercentage: .325,
          categoryPercentage: .5
        }
         ]
       },
       options: {
         maintainAspectRatio: false,
         cornerRadius: 15,
         legend: {
           display: true
         },
         scales: {
           yAxes: [{
             ticks: {
                  beginAtZero: true
             },
             gridLines: {
               display: false
             },
             stacked: false,
             stacked: true,
           }],
           xAxes: [{
             stacked: false,
             gridLines: {
               color: "transparent"
             },
             stacked: true,
           }]
         }
       }
     });
   },

  renderStudentFeeHeadsCountChart : function(feeHeadLable, allBranches, feeHeadMap){
    const colors = ["#2e7078", "#43a2ad", "#7fadb2", "#a5d4d9", "#afdbe0", "#e7f9fb"];

    const datasets = allBranches.map((institute, index) => {
        const color = colors[index % colors.length]; 
        return {
          // we have used split to display institute name as it also contain unique code
            label: institute.split(":")[1], 
            backgroundColor: color,
            borderColor: color,
            hoverBackgroundColor: color,
            hoverBorderColor: color,
            data: feeHeadLable.map(feeHead => feeHeadMap[feeHead][institute] || 0), 
            barPercentage: 0.325,
            categoryPercentage: 0.5
        };
    });
      new Chart($("#chartjs-institute-fee-head-distribution"), {
        type: "bar",
        data: {
            labels: feeHeadLable,
            datasets: datasets 
        },
        options: {
          maintainAspectRatio: false,
          cornerRadius: 15,
          legend: {
            display: true
          },
          scales: {
            yAxes: [{
              ticks: {
                   beginAtZero: true
              },
              gridLines: {
                display: false
              },
              stacked: false,
              stacked: true,
            }],
            xAxes: [{
              stacked: false,
              gridLines: {
                color: "transparent"
              },
              stacked: true,
            }]
          }
        }
    });
   },

   renderStaffTodayAttendanceChart : function(labelArr, todayStaffPresentArr, totalStaffPresent) {
    new Chart($("#chartjs-staff-present-attendance-pie"), {
      type: "doughnut",
      data: {
        labels: labelArr,
        datasets: [{
          data: todayStaffPresentArr,
          backgroundColor: ["#2e7078", "#43a2ad", "#7fadb2", "#a5d4d9", "#afdbe0", "#e7f9fb"],
          borderColor: "transparent"
        }]
      },
      options: {
        rotation: 1 * Math.PI,
             circumference: 1 * Math.PI,
        maintainAspectRatio: false,
        cutoutPercentage: 70,
        // rotation: -90,
          // circumference: 180,
        legend: {
          display: false
        }
      },
      plugins: [{
        id: 'customPlugin',
        beforeDraw: (chart, args, options) => {
          var width = chart.chart.width,
              height = chart.chart.height,
              ctx = chart.chart.ctx;

          ctx.restore();
          var fontSize = (height / 114).toFixed(2);
          ctx.font = fontSize + "em sans-serif";
          ctx.textBaseline = "middle";

          var text = totalStaffPresent,
              textX = Math.round((width - ctx.measureText(text).width) / 2),
              textY = height / 2;

          ctx.fillText(text, textX, textY);
          ctx.save();
        }
      }]
    });
   },

   generateBirthdayCertificate : function (ref) {
     var student = JSON.parse($(ref).find(".student-info").text());
     var instituteId = student.instituteId;
     var sessionId = student.studentAcademicSessionInfoResponse.academicSession.academicSessionId;
     var studentId = student.studentId;
     window.open(baseURL + "/organisation-portal/generate-birthday-certificate/"+instituteId+"/"+sessionId+"/"+studentId, '_blank');
   },

};

function loadChangePasswordPage() {
    ajaxClient.get("/organisation-portal/change-password", function(data) {
        $("#main-content").html(data);
    });
}

function changePassword(){
  var oldPassword = $("#old-password").val();
  var newPassword = $("#new-password").val();
  var confirmNewPassword = $("#confirm-new-password").val();
  var changePasswordInfo = {"oldPassword" : oldPassword, "newPassword" : newPassword};

  if(newPassword != confirmNewPassword){
    showErrorDialogBox("Password don't match!!");
    return;
  }

  ajaxClient.post("/organisation-portal/update-password",{'changePasswordInfo':JSON.stringify(changePasswordInfo)}, function(data) {
    $("#change-password\\.status-modal-container").html(data);
    $("#change-password-status-modal").modal('toggle');
    $("#old-password").val("");
    $("#new-password").val("");
    $("#confirm-new-password").val("");
  });
}

function formatINRCurrency (number) {
  if(number == null || number == undefined || number == "") {
    return 0;
  }
  number = number.toLocaleString('en-IN');
  return number;
}

function viewStatistics () {
  loadHomePage();
  $("#sidebar").removeClass("collapsed");
}

function viewStatisticsV2() {
  homePageV2.loadHomePage();
  $("#sidebar").removeClass("collapsed");
}


var menuLoader = {

  registerSidebarMenu : function () {
      menuLoader.registerFeesReportsMenu();
  },

  registerFeesReportsMenu : function () {
      $('#orgFeesReportNav').on('click', function() {
          orgFeesReport.loadHomePage();
      });
  },

}

var orgFeesReport = {

  dataCache : {},

  loadHomePage: function (){
      ajaxClient.get("/organisation-portal/fees-reports", function(data) {
          $("#main-content").html(data);
          initDateWithYearRange("-5:+5", true);
          initSelect2("All");
          orgFeesReport.initDataCache();
          commonUtils.bindCardHoverEvent();
          commonUtils.bindReportCardClickEvent();
          orgFeesReport.bindGenerateReportEvent();
          reportUtils.bindSelectClassCheckboxEvent();
      });
  },

  initDataCache : function () {
    var allSessions = readJson("#all-sessions");
    var currentSession = readJson("#selected-academic-session-json");

    orgFeesReport.dataCache.allSessions = allSessions;

    $(".report-academic-session").val(currentSession.academicSessionId);

  },

  bindGenerateReportEvent: function (){

    $('.generate-report').on('click', function () {
        var containerElement = $(this).closest('div.report-field-container');
        var requiredHeadersCSV = reportUtils.getReportHeadersCSV(containerElement);
        var invalid = validateMandatoryFields($(containerElement));
        if(invalid){
          return;
        }

        var academicSession = "";
        if($(containerElement).find(".report-academic-session option:selected").length > 0){
          academicSession = $(containerElement).find(".report-academic-session option:selected").val();
        }

        academicSession = academicSession === "" ? 0 : academicSession;

        var reportType = $(containerElement).find('p.report-type').text().trim();

        var studentStatus = "";
          if($(containerElement).find(".reports-student-status").length > 0){
            studentStatus = $(containerElement).find(".reports-student-status").val().join();
        }

        $(this).closest('div.modal').modal('toggle');
        window.open(baseURL+"/organisation-portal/fees-generate-report/"+reportType+"?academic_session_id="+academicSession+"&studentStatus="+studentStatus, '_blank');
    });
  }

};
